import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { UserProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  forceRefreshProfile: () => Promise<void>
  testProfileFetch?: () => Promise<UserProfile | null>
  testDirectFetch?: () => Promise<{ data: any; error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  console.log('AuthProvider component mounting...')
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)



  const fetchUserProfile = async (userId: string, retryCount = 0): Promise<UserProfile | null> => {
    try {
      console.log(`Fetching user profile for userId: ${userId}, attempt: ${retryCount + 1}`)

      // Get current session to ensure we have the latest auth state
      const { data: sessionData } = await supabase.auth.getSession()
      console.log('Current session for profile fetch:', sessionData.session?.user?.id)

      if (!sessionData.session) {
        console.error('No active session for profile fetch')
        return null
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId
        })

        // If it's a network error or server error, retry up to 3 times
        if (retryCount < 3 && (error.message.includes('Failed to fetch') || error.code === 'PGRST301')) {
          console.log(`Retrying profile fetch in 1 second... (attempt ${retryCount + 1}/3)`)
          await new Promise(resolve => setTimeout(resolve, 1000))
          return fetchUserProfile(userId, retryCount + 1)
        }

        // If profile doesn't exist, create a default one for existing users
        if (error.code === 'PGRST116' || error.message.includes('No rows found')) {
          console.log('No profile found, creating default profile...')
          try {
            // Get user email to determine if this is the super admin
            const { data: sessionData } = await supabase.auth.getSession()
            const userEmail = sessionData.session?.user?.email

            // Determine role based on email
            const role = userEmail === '<EMAIL>' ? 'super_admin' : 'user'
            console.log(`Creating profile for ${userEmail} with role: ${role}`)

            const { data: newProfile, error: createError } = await supabase
              .from('user_profiles')
              .insert({
                user_id: userId,
                role: role,
                company_id: null
              })
              .select()
              .single()

            if (createError) {
              console.error('Error creating default profile:', createError)
              return null
            }

            console.log('Created default profile:', newProfile)
            return newProfile
          } catch (createErr) {
            console.error('Failed to create default profile:', createErr)
            return null
          }
        }

        return null
      }

      console.log('Profile fetched successfully:', data)
      return data
    } catch (error) {
      console.error('Unexpected error fetching user profile:', error)

      // Retry on network errors
      if (retryCount < 3) {
        console.log(`Retrying profile fetch due to network error... (attempt ${retryCount + 1}/3)`)
        await new Promise(resolve => setTimeout(resolve, 1000))
        return fetchUserProfile(userId, retryCount + 1)
      }

      return null
    }
  }

  const refreshProfile = async () => {
    if (user) {
      const userProfile = await fetchUserProfile(user.id)
      setProfile(userProfile)
    }
  }

  const forceRefreshProfile = async () => {
    if (user) {
      console.log('Force refreshing profile for user:', user.id)
      setLoading(true)
      try {
        const userProfile = await fetchUserProfile(user.id)
        setProfile(userProfile)
        console.log('Force refresh completed:', userProfile)
      } catch (error) {
        console.error('Error during force refresh:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  useEffect(() => {
    console.log('AuthContext useEffect running...')

    let mounted = true
    let timeoutId: NodeJS.Timeout

    // Get initial session
    const initializeAuth = async () => {
      try {
        setLoading(true)
        const { data: { session }, error } = await supabase.auth.getSession()
        console.log('Session retrieved:', session, 'Error:', error)

        if (!mounted) return

        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          console.log('Fetching profile for user:', session.user.id)
          const profile = await fetchUserProfile(session.user.id)
          console.log('Profile fetched in initializeAuth:', profile)
          if (mounted) {
            setProfile(profile)
            if (!profile) {
              console.error('Failed to fetch profile during initialization')
            }
          }
        } else {
          console.log('No user session')
          if (mounted) {
            setProfile(null)
          }
        }
      } catch (error) {
        console.error('Error getting session:', error)
        if (mounted) {
          setSession(null)
          setUser(null)
          setProfile(null)
        }
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    // Fallback timeout to ensure loading is set to false (increased to 10 seconds)
    timeoutId = setTimeout(() => {
      if (mounted) {
        console.log('Timeout reached, forcing loading to false')
        setLoading(false)
      }
    }, 10000)

    initializeAuth()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email)

      if (!mounted) return

      // Clear timeout since we got an auth state change
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        setLoading(true)
        try {
          const userProfile = await fetchUserProfile(session.user.id)
          if (mounted) {
            setProfile(userProfile)
          }
        } catch (error) {
          console.error('Error fetching profile on auth change:', error)
          if (mounted) {
            setProfile(null)
          }
        } finally {
          if (mounted) {
            setLoading(false)
          }
        }
      } else {
        if (mounted) {
          setProfile(null)
          setLoading(false)
        }
      }
    })

    return () => {
      mounted = false
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signOut = async () => {
    try {
      console.log('Signing out...')
      setLoading(true)

      // Clear local state immediately
      setUser(null)
      setProfile(null)
      setSession(null)

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Error signing out:', error)
        throw error
      }

      console.log('Successfully signed out')
    } catch (error) {
      console.error('Sign out error:', error)
      // Even if there's an error, clear local state
      setUser(null)
      setProfile(null)
      setSession(null)
    } finally {
      setLoading(false)
    }
  }

  // Debug function to test profile fetching
  const testProfileFetch = async () => {
    if (user) {
      console.log('Testing profile fetch for user:', user.id)
      const profile = await fetchUserProfile(user.id)
      console.log('Test result:', profile)
      return profile
    }
    console.log('No user to test with')
    return null
  }

  // Simple direct test
  const testDirectFetch = async () => {
    console.log('🔥 DIRECT TEST BUTTON CLICKED!')
    try {
      console.log('Testing direct Supabase fetch...')

      // First check session
      const { data: sessionData } = await supabase.auth.getSession()
      console.log('Session check:', sessionData.session?.user?.id)

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', '263ab3e1-ead7-46f5-a724-aaf498a685d5')
        .single()

      console.log('Direct fetch result:', { data, error })
      return { data, error }
    } catch (err) {
      console.error('Direct fetch exception:', err)
      return { data: null, error: err }
    }
  }

  // Make test functions available globally for debugging
  if (typeof window !== 'undefined') {
    (window as any).testProfileFetch = testProfileFetch
    (window as any).testDirectFetch = testDirectFetch
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signIn,
    signOut,
    refreshProfile,
    forceRefreshProfile,
    testProfileFetch,
    testDirectFetch
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
